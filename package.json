{"name": "devassist-backend", "version": "1.0.0", "description": "AI-powered multi-role web IDE backend for African developers, teams, and SMEs", "type": "module", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["nodejs", "express", "mongodb", "ai", "ide", "anthropic", "claude"], "author": "DevAssist Team", "license": "MIT", "dependencies": {"@huggingface/inference": "^4.7.1", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "socket.io": "^4.7.4"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.4.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}