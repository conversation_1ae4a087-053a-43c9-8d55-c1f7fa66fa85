{"$id": "/inference/schemas/chat-completion/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Chat Completion Input.\n\nAuto-generated from TGI specs.\nFor more details, check out https://github.com/huggingface/huggingface.js/blob/main/packages/tasks/scripts/inference-tgi-import.ts.", "title": "ChatCompletionInput", "type": "object", "required": ["messages"], "properties": {"frequency_penalty": {"type": "number", "format": "float", "description": "Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far,\ndecreasing the model's likelihood to repeat the same line verbatim.", "example": "1.0", "nullable": true}, "logit_bias": {"type": "array", "items": {"type": "number", "format": "float"}, "description": "UNUSED\nModify the likelihood of specified tokens appearing in the completion. Accepts a JSON object that maps tokens\n(specified by their token ID in the tokenizer) to an associated bias value from -100 to 100. Mathematically,\nthe bias is added to the logits generated by the model prior to sampling. The exact effect will vary per model,\nbut values between -1 and 1 should decrease or increase likelihood of selection; values like -100 or 100 should\nresult in a ban or exclusive selection of the relevant token.", "nullable": true}, "logprobs": {"type": "boolean", "description": "Whether to return log probabilities of the output tokens or not. If true, returns the log probabilities of each\noutput token returned in the content of message.", "example": "false", "nullable": true}, "max_tokens": {"type": "integer", "format": "int32", "description": "The maximum number of tokens that can be generated in the chat completion.", "default": "1024", "example": "32", "nullable": true, "minimum": 0}, "messages": {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionInputMessage"}, "description": "A list of messages comprising the conversation so far.", "example": "[{\"role\": \"user\", \"content\": \"What is Deep Learning?\"}]"}, "model": {"type": "string", "description": "[UNUSED] ID of the model to use. See the model endpoint compatibility table for details on which models work with the Chat API.", "example": "mistralai/Mistral-7B-Instruct-v0.2", "nullable": true}, "n": {"type": "integer", "format": "int32", "description": "UNUSED\nHow many chat completion choices to generate for each input message. Note that you will be charged based on the\nnumber of generated tokens across all of the choices. Keep n as 1 to minimize costs.", "example": "2", "nullable": true, "minimum": 0}, "presence_penalty": {"type": "number", "format": "float", "description": "Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far,\nincreasing the model's likelihood to talk about new topics", "example": 0.1, "nullable": true}, "response_format": {"allOf": [{"$ref": "#/$defs/ChatCompletionInputGrammarType"}], "default": "null", "nullable": true}, "seed": {"type": "integer", "format": "int64", "example": 42, "nullable": true, "minimum": 0}, "stop": {"type": "array", "items": {"type": "string"}, "description": "Up to 4 sequences where the API will stop generating further tokens.", "example": "null", "nullable": true}, "stream": {"type": "boolean"}, "stream_options": {"allOf": [{"$ref": "#/$defs/ChatCompletionInputStreamOptions"}], "nullable": true}, "temperature": {"type": "number", "format": "float", "description": "What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while\nlower values like 0.2 will make it more focused and deterministic.\n\nWe generally recommend altering this or `top_p` but not both.", "example": 1, "nullable": true}, "tool_choice": {"allOf": [{"$ref": "#/$defs/ChatCompletionInputToolChoice"}], "default": "auto", "nullable": true}, "tool_prompt": {"type": "string", "description": "A prompt to be appended before the tools", "example": "Given the functions available, please respond with a JSON for a function call with its proper arguments that best answers the given prompt. Respond in the format {name: function name, parameters: dictionary of argument name and its value}.Do not use variables.", "nullable": true}, "tools": {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionInputTool"}, "description": "A list of tools the model may call. Currently, only functions are supported as a tool. Use this to provide a list of\nfunctions the model may generate JSON inputs for.", "example": "null", "nullable": true}, "top_logprobs": {"type": "integer", "format": "int32", "description": "An integer between 0 and 5 specifying the number of most likely tokens to return at each token position, each with\nan associated log probability. logprobs must be set to true if this parameter is used.", "example": "5", "nullable": true, "minimum": 0}, "top_p": {"type": "number", "format": "float", "description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the\ntokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.", "example": 0.95, "nullable": true}}, "$defs": {"ChatCompletionInputMessage": {"allOf": [{"$ref": "#/$defs/ChatCompletionInputMessageBody"}, {"type": "object", "required": ["role"], "properties": {"name": {"type": "string", "example": "\"<PERSON>\"", "nullable": true}, "role": {"type": "string", "example": "user"}}}], "title": "ChatCompletionInputMessage"}, "ChatCompletionInputMessageBody": {"oneOf": [{"type": "object", "required": ["content"], "properties": {"content": {"$ref": "#/$defs/ChatCompletionInputMessageContent"}}}, {"type": "object", "required": ["tool_calls"], "properties": {"tool_calls": {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionInputToolCall"}}}}], "title": "ChatCompletionInputMessageBody"}, "ChatCompletionInputMessageContent": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"$ref": "#/$defs/ChatCompletionInputMessageChunk"}}], "title": "ChatCompletionInputMessageContent"}, "ChatCompletionInputMessageChunk": {"oneOf": [{"type": "object", "required": ["text", "type"], "properties": {"text": {"type": "string"}, "type": {"type": "string", "enum": ["text"]}}}, {"type": "object", "required": ["image_url", "type"], "properties": {"image_url": {"$ref": "#/$defs/ChatCompletionInputUrl"}, "type": {"type": "string", "enum": ["image_url"]}}}], "discriminator": {"propertyName": "type"}, "title": "ChatCompletionInputMessageChunk"}, "ChatCompletionInputUrl": {"type": "object", "required": ["url"], "properties": {"url": {"type": "string"}}, "title": "ChatCompletionInputUrl"}, "ChatCompletionInputToolCall": {"type": "object", "required": ["id", "type", "function"], "properties": {"function": {"$ref": "#/$defs/ChatCompletionInputFunctionDefinition"}, "id": {"type": "string"}, "type": {"type": "string"}}, "title": "ChatCompletionInputToolCall"}, "ChatCompletionInputFunctionDefinition": {"type": "object", "required": ["name"], "properties": {"parameters": {}, "description": {"type": "string", "nullable": true}, "name": {"type": "string"}}, "title": "ChatCompletionInputFunctionDefinition"}, "ChatCompletionInputGrammarType": {"oneOf": [{"$ref": "#/$defs/ChatCompletionInputResponseFormatText"}, {"$ref": "#/$defs/ChatCompletionInputResponseFormatJSONSchema"}, {"$ref": "#/$defs/ChatCompletionInputResponseFormatJSONObject"}], "title": "ChatCompletionInputGrammarType"}, "ChatCompletionInputResponseFormatText": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["text"]}}, "title": "ChatCompletionInputResponseFormatText"}, "ChatCompletionInputResponseFormatJSONSchema": {"type": "object", "required": ["type", "json_schema"], "properties": {"type": {"type": "string", "enum": ["json_schema"]}, "json_schema": {"$ref": "#/$defs/ChatCompletionInputJsonSchemaConfig"}}, "title": "ChatCompletionInputResponseFormatJSONSchema"}, "ChatCompletionInputResponseFormatJSONObject": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["json_object"]}}, "title": "ChatCompletionInputResponseFormatJSONObject"}, "ChatCompletionInputJsonSchemaConfig": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "The name of the response format."}, "description": {"type": "string", "description": "A description of what the response format is for, used by the model to determine how to respond in the format.", "nullable": true}, "schema": {"type": "object", "description": "The schema for the response format, described as a JSON Schema object. Learn how to build JSON schemas [here](https://json-schema.org/).", "nullable": true}, "strict": {"type": "boolean", "description": "Whether to enable strict schema adherence when generating the output. If set to true, the model will always follow the exact schema defined in the `schema` field.", "nullable": true}}, "title": "ChatCompletionInputJsonSchemaConfig"}, "ChatCompletionInputStreamOptions": {"type": "object", "properties": {"include_usage": {"type": "boolean", "description": "If set, an additional chunk will be streamed before the data: [<PERSON>ON<PERSON>] message. The usage field on this chunk shows the token usage statistics for the entire request, and the choices field will always be an empty array. All other chunks will also include a usage field, but with a null value.", "example": "true"}}, "title": "ChatCompletionInputStreamOptions"}, "ChatCompletionInputToolChoice": {"oneOf": [{"type": "string", "description": "Means the model can pick between generating a message or calling one or more tools.", "enum": ["auto"]}, {"type": "string", "description": "Means the model will not call any tool and instead generates a message.", "enum": ["none"]}, {"type": "string", "description": "Means the model must call one or more tools.", "enum": ["required"]}, {"type": "object", "required": ["function"], "properties": {"function": {"$ref": "#/$defs/ChatCompletionInputFunctionName"}}}], "description": "<https://platform.openai.com/docs/guides/function-calling/configuring-function-calling-behavior-using-the-tool_choice-parameter>", "title": "ChatCompletionInputToolChoice"}, "ChatCompletionInputFunctionName": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}, "title": "ChatCompletionInputFunctionName"}, "ChatCompletionInputTool": {"type": "object", "required": ["type", "function"], "properties": {"function": {"$ref": "#/$defs/ChatCompletionInputFunctionDefinition"}, "type": {"type": "string", "example": "function"}}, "title": "ChatCompletionInputTool"}}}