{"name": "@huggingface/inference", "version": "4.7.1", "license": "MIT", "author": "Hugging Face and <PERSON> <tim.mikelad<PERSON>@gmail.com>", "description": "Typescript client for the Hugging Face Inference Providers and Inference Endpoints", "repository": {"type": "git", "url": "https://github.com/huggingface/huggingface.js.git"}, "publishConfig": {"access": "public"}, "keywords": ["hugging face", "hugging face typescript", "huggingface", "huggingface-inference-api", "huggingface-inference-api-typescript", "inference", "ai"], "engines": {"node": ">=18"}, "files": ["dist", "src", "!src/snippets/templates/**/*.jinja"], "source": "src/index.ts", "types": "./dist/commonjs/index.d.ts", "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "type": "module", "dependencies": {"@huggingface/tasks": "^0.19.35", "@huggingface/jinja": "^0.5.1"}, "devDependencies": {"@types/node": "18.13.0"}, "resolutions": {}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"build": "pnpm run export-templates && pnpm run package-to-ts && tshy", "lint": "eslint --quiet --fix --ext .cjs,.ts .", "lint:check": "eslint --ext .cjs,.ts .", "format": "prettier --write .", "format:check": "prettier --check .", "test": "vitest run --config vitest.config.mts", "test:browser": "vitest run --browser.name=chrome --browser.headless --config vitest.config.mts", "check": "tsc", "dev": "pnpm run export-templates && tsup src/index.ts --format cjs,esm --watch", "export-templates": "tsx scripts/export-templates.ts", "package-to-ts": "tsx scripts/package-json-to-ts.ts"}}