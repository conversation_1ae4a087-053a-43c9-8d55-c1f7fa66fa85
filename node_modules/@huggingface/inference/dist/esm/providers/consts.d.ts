import type { InferenceProvider, InferenceProviderMappingEntry } from "../types.js";
import { type ModelId } from "../types.js";
/**
 * If you want to try to run inference for a new model locally before it's registered on huggingface.co
 * for a given Inference Provider,
 * you can add it to the following dictionary, for dev purposes.
 *
 * We also inject into this dictionary from tests.
 */
export declare const HARDCODED_MODEL_INFERENCE_MAPPING: Record<InferenceProvider, Record<ModelId, InferenceProviderMappingEntry>>;
//# sourceMappingURL=consts.d.ts.map