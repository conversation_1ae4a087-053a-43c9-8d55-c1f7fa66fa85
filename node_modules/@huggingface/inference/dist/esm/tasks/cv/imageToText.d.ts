import type { ImageToTextInput, ImageToTextOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
import type { LegacyImageInput } from "./utils.js";
export type ImageToTextArgs = BaseArgs & (ImageToTextInput | LegacyImageInput);
/**
 * This task reads some image input and outputs the text caption.
 */
export declare function imageToText(args: ImageToTextArgs, options?: Options): Promise<ImageToTextOutput>;
//# sourceMappingURL=imageToText.d.ts.map