import type { ImageClassificationInput, ImageClassificationOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
import { type LegacyImageInput } from "./utils.js";
export type ImageClassificationArgs = BaseArgs & (ImageClassificationInput | LegacyImageInput);
/**
 * This task reads some image input and outputs the likelihood of classes.
 * Recommended model: google/vit-base-patch16-224
 */
export declare function imageClassification(args: ImageClassificationArgs, options?: Options): Promise<ImageClassificationOutput>;
//# sourceMappingURL=imageClassification.d.ts.map