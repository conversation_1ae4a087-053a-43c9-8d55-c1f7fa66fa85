// Custom tasks with arbitrary inputs and outputs
export * from "./custom/request.js";
export * from "./custom/streamingRequest.js";
// Audio tasks
export * from "./audio/audioClassification.js";
export * from "./audio/audioToAudio.js";
export * from "./audio/automaticSpeechRecognition.js";
export * from "./audio/textToSpeech.js";
// Computer Vision tasks
export * from "./cv/imageClassification.js";
export * from "./cv/imageSegmentation.js";
export * from "./cv/imageToImage.js";
export * from "./cv/imageToText.js";
export * from "./cv/imageToVideo.js";
export * from "./cv/objectDetection.js";
export * from "./cv/textToImage.js";
export * from "./cv/textToVideo.js";
export * from "./cv/zeroShotImageClassification.js";
// Natural Language Processing tasks
export * from "./nlp/chatCompletion.js";
export * from "./nlp/chatCompletionStream.js";
export * from "./nlp/featureExtraction.js";
export * from "./nlp/fillMask.js";
export * from "./nlp/questionAnswering.js";
export * from "./nlp/sentenceSimilarity.js";
export * from "./nlp/summarization.js";
export * from "./nlp/tableQuestionAnswering.js";
export * from "./nlp/textClassification.js";
export * from "./nlp/textGeneration.js";
export * from "./nlp/textGenerationStream.js";
export * from "./nlp/tokenClassification.js";
export * from "./nlp/translation.js";
export * from "./nlp/zeroShotClassification.js";
// Multimodal tasks
export * from "./multimodal/documentQuestionAnswering.js";
export * from "./multimodal/visualQuestionAnswering.js";
// Tabular tasks
export * from "./tabular/tabularClassification.js";
export * from "./tabular/tabularRegression.js";
