import type { TranslationInput, TranslationOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type TranslationArgs = BaseArgs & TranslationInput;
/**
 * This task is well known to translate text from one language to another. Recommended model: Helsinki-NLP/opus-mt-ru-en.
 */
export declare function translation(args: TranslationArgs, options?: Options): Promise<TranslationOutput>;
//# sourceMappingURL=translation.d.ts.map