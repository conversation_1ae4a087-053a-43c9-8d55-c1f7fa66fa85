// Generated file - do not edit directly
export const templates = {
    "js": {
        "fetch": {
            "basic": "async function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"application/json\",\n{% if billTo %}\n\t\t\t\t\"X-HF-Bill-To\": \"{{ billTo }}\",\n{% endif %}\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(data),\n\t\t}\n\t);\n\tconst result = await response.json();\n\treturn result;\n}\n\nquery({ inputs: {{ providerInputs.asObj.inputs }} }).then((response) => {\n    console.log(JSON.stringify(response));\n});",
            "basicAudio": "async function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"audio/flac\",\n{% if billTo %}\n\t\t\t\t\"X-HF-Bill-To\": \"{{ billTo }}\",\n{% endif %}\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(data),\n\t\t}\n\t);\n\tconst result = await response.json();\n\treturn result;\n}\n\nquery({ inputs: {{ providerInputs.asObj.inputs }} }).then((response) => {\n    console.log(JSON.stringify(response));\n});",
            "basicImage": "async function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"image/jpeg\",\n{% if billTo %}\n\t\t\t\t\"X-HF-Bill-To\": \"{{ billTo }}\",\n{% endif %}\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(data),\n\t\t}\n\t);\n\tconst result = await response.json();\n\treturn result;\n}\n\nquery({ inputs: {{ providerInputs.asObj.inputs }} }).then((response) => {\n    console.log(JSON.stringify(response));\n});",
            "conversational": "async function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"application/json\",\n{% if billTo %}\n\t\t\t\t\"X-HF-Bill-To\": \"{{ billTo }}\",\n{% endif %}\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(data),\n\t\t}\n\t);\n\tconst result = await response.json();\n\treturn result;\n}\n\nquery({ \n{{ autoInputs.asTsString }}\n}).then((response) => {\n    console.log(JSON.stringify(response));\n});",
            "imageToImage": "const image = fs.readFileSync(\"{{inputs.asObj.inputs}}\");\n\nasync function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"image/jpeg\",\n{% if billTo %}\n\t\t\t\t\"X-HF-Bill-To\": \"{{ billTo }}\",\n{% endif %}\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: {\n\t\t\t\t\"inputs\": `data:image/png;base64,${data.inputs.encode(\"base64\")}`,\n\t\t\t\t\"parameters\": data.parameters,\n\t\t\t}\n\t\t}\n\t);\n\tconst result = await response.json();\n\treturn result;\n}\n\nquery({ \n\tinputs: image,\n\tparameters: {\n\t\tprompt: \"{{ inputs.asObj.parameters.prompt }}\",\n\t}\n}).then((response) => {\n    console.log(JSON.stringify(response));\n});",
            "imageToVideo": "const image = fs.readFileSync(\"{{inputs.asObj.inputs}}\");\n\nasync function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"image/jpeg\",\n{% if billTo %}\n\t\t\t\t\"X-HF-Bill-To\": \"{{ billTo }}\",\n{% endif %}\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: {\n\t\t\t\t\"image_url\": `data:image/png;base64,${data.image.encode(\"base64\")}`,\n\t\t\t\t\"prompt\": data.prompt,\n\t\t\t}\n\t\t}\n\t);\n\tconst result = await response.json();\n\treturn result;\n}\n\nquery({\n\t\"image\": image,\n\t\"prompt\": \"{{inputs.asObj.parameters.prompt}}\",\n}).then((response) => {\n    // Use video\n});",
            "textToAudio": "{% if model.library_name == \"transformers\" %}\nasync function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"application/json\",\n{% if billTo %}\n\t\t\t\t\"X-HF-Bill-To\": \"{{ billTo }}\",\n{% endif %}\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(data),\n\t\t}\n\t);\n\tconst result = await response.blob();\n    return result;\n}\n\nquery({ inputs: {{ providerInputs.asObj.inputs }} }).then((response) => {\n    // Returns a byte object of the Audio wavform. Use it directly!\n});\n{% else %}\nasync function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(data),\n\t\t}\n\t);\n    const result = await response.json();\n    return result;\n}\n\nquery({ inputs: {{ providerInputs.asObj.inputs }} }).then((response) => {\n    console.log(JSON.stringify(response));\n});\n{% endif %} ",
            "textToImage": "async function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"application/json\",\n{% if billTo %}\n\t\t\t\t\"X-HF-Bill-To\": \"{{ billTo }}\",\n{% endif %}\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(data),\n\t\t}\n\t);\n\tconst result = await response.blob();\n\treturn result;\n}\n\n\nquery({ {{ providerInputs.asTsString }} }).then((response) => {\n    // Use image\n});",
            "textToSpeech": "{% if model.library_name == \"transformers\" %}\nasync function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"application/json\",\n{% if billTo %}\n\t\t\t\t\"X-HF-Bill-To\": \"{{ billTo }}\",\n{% endif %}\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(data),\n\t\t}\n\t);\n\tconst result = await response.blob();\n    return result;\n}\n\nquery({ text: {{ inputs.asObj.inputs }} }).then((response) => {\n    // Returns a byte object of the Audio wavform. Use it directly!\n});\n{% else %}\nasync function query(data) {\n\tconst response = await fetch(\n\t\t\"{{ fullUrl }}\",\n\t\t{\n\t\t\theaders: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t},\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(data),\n\t\t}\n\t);\n    const result = await response.json();\n    return result;\n}\n\nquery({ text: {{ inputs.asObj.inputs }} }).then((response) => {\n    console.log(JSON.stringify(response));\n});\n{% endif %} ",
            "zeroShotClassification": "async function query(data) {\n    const response = await fetch(\n\t\t\"{{ fullUrl }}\",\n        {\n            headers: {\n\t\t\t\tAuthorization: \"{{ authorizationHeader }}\",\n                \"Content-Type\": \"application/json\",\n{% if billTo %}\n                \"X-HF-Bill-To\": \"{{ billTo }}\",\n{% endif %}         },\n            method: \"POST\",\n            body: JSON.stringify(data),\n        }\n    );\n    const result = await response.json();\n    return result;\n}\n\nquery({\n    inputs: {{ providerInputs.asObj.inputs }},\n    parameters: { candidate_labels: [\"refund\", \"legal\", \"faq\"] }\n}).then((response) => {\n    console.log(JSON.stringify(response));\n});"
        },
        "huggingface.js": {
            "basic": "import { InferenceClient } from \"@huggingface/inference\";\n\nconst client = new InferenceClient(\"{{ accessToken }}\");\n\nconst output = await client.{{ methodName }}({\n{% if endpointUrl %}\n    endpointUrl: \"{{ endpointUrl }}\",\n{% endif %}\n\tmodel: \"{{ model.id }}\",\n\tinputs: {{ inputs.asObj.inputs }},\n\tprovider: \"{{ provider }}\",\n}{% if billTo %}, {\n\tbillTo: \"{{ billTo }}\",\n}{% endif %});\n\nconsole.log(output);",
            "basicAudio": "import { InferenceClient } from \"@huggingface/inference\";\n\nconst client = new InferenceClient(\"{{ accessToken }}\");\n\nconst data = fs.readFileSync({{inputs.asObj.inputs}});\n\nconst output = await client.{{ methodName }}({\n{% if endpointUrl %}\n    endpointUrl: \"{{ endpointUrl }}\",\n{% endif %}\n\tdata,\n\tmodel: \"{{ model.id }}\",\n\tprovider: \"{{ provider }}\",\n}{% if billTo %}, {\n\tbillTo: \"{{ billTo }}\",\n}{% endif %});\n\nconsole.log(output);",
            "basicImage": "import { InferenceClient } from \"@huggingface/inference\";\n\nconst client = new InferenceClient(\"{{ accessToken }}\");\n\nconst data = fs.readFileSync({{inputs.asObj.inputs}});\n\nconst output = await client.{{ methodName }}({\n{% if endpointUrl %}\n    endpointUrl: \"{{ endpointUrl }}\",\n{% endif %}\n\tdata,\n\tmodel: \"{{ model.id }}\",\n\tprovider: \"{{ provider }}\",\n}{% if billTo %}, {\n\tbillTo: \"{{ billTo }}\",\n}{% endif %});\n\nconsole.log(output);",
            "conversational": "import { InferenceClient } from \"@huggingface/inference\";\n\nconst client = new InferenceClient(\"{{ accessToken }}\");\n\nconst chatCompletion = await client.chatCompletion({\n{% if endpointUrl %}\n    endpointUrl: \"{{ endpointUrl }}\",\n{% endif %}\n    provider: \"{{ provider }}\",\n    model: \"{{ model.id }}\",\n{{ inputs.asTsString }}\n}{% if billTo %}, {\n    billTo: \"{{ billTo }}\",\n}{% endif %});\n\nconsole.log(chatCompletion.choices[0].message);",
            "conversationalStream": "import { InferenceClient } from \"@huggingface/inference\";\n\nconst client = new InferenceClient(\"{{ accessToken }}\");\n\nlet out = \"\";\n\nconst stream = client.chatCompletionStream({\n{% if endpointUrl %}\n    endpointUrl: \"{{ endpointUrl }}\",\n{% endif %}\n    provider: \"{{ provider }}\",\n    model: \"{{ model.id }}\",\n{{ inputs.asTsString }}\n}{% if billTo %}, {\n    billTo: \"{{ billTo }}\",\n}{% endif %});\n\nfor await (const chunk of stream) {\n\tif (chunk.choices && chunk.choices.length > 0) {\n\t\tconst newContent = chunk.choices[0].delta.content;\n\t\tout += newContent;\n\t\tconsole.log(newContent);\n\t}\n}",
            "imageToImage": "import { InferenceClient } from \"@huggingface/inference\";\n\nconst client = new InferenceClient(\"{{ accessToken }}\");\n\nconst data = fs.readFileSync(\"{{inputs.asObj.inputs}}\");\n\nconst image = await client.imageToImage({\n{% if endpointUrl %}\n\tendpointUrl: \"{{ endpointUrl }}\",\n{% endif %}\n\tprovider: \"{{provider}}\",\n\tmodel: \"{{model.id}}\",\n\tinputs: data,\n\tparameters: { prompt: \"{{inputs.asObj.parameters.prompt}}\", },\n}{% if billTo %}, {\n\tbillTo: \"{{ billTo }}\",\n}{% endif %});\n/// Use the generated image (it's a Blob)\n// For example, you can save it to a file or display it in an image element\n",
            "imageToVideo": "import { InferenceClient } from \"@huggingface/inference\";\n\nconst client = new InferenceClient(\"{{ accessToken }}\");\n\nconst data = fs.readFileSync(\"{{inputs.asObj.inputs}}\");\n\nconst video = await client.imageToVideo({\n{% if endpointUrl %}\n\tendpointUrl: \"{{ endpointUrl }}\",\n{% endif %}\n\tprovider: \"{{provider}}\",\n\tmodel: \"{{model.id}}\",\n\tinputs: data,\n\tparameters: { prompt: \"{{inputs.asObj.parameters.prompt}}\", },\n}{% if billTo %}, {\n\tbillTo: \"{{ billTo }}\",\n}{% endif %});\n\n/// Use the generated video (it's a Blob)\n// For example, you can save it to a file or display it in a video element\n",
            "textToImage": "import { InferenceClient } from \"@huggingface/inference\";\n\nconst client = new InferenceClient(\"{{ accessToken }}\");\n\nconst image = await client.textToImage({\n{% if endpointUrl %}\n    endpointUrl: \"{{ endpointUrl }}\",\n{% endif %}\n    provider: \"{{ provider }}\",\n    model: \"{{ model.id }}\",\n\tinputs: {{ inputs.asObj.inputs }},\n\tparameters: { num_inference_steps: 5 },\n}{% if billTo %}, {\n    billTo: \"{{ billTo }}\",\n}{% endif %});\n/// Use the generated image (it's a Blob)",
            "textToSpeech": "import { InferenceClient } from \"@huggingface/inference\";\n\nconst client = new InferenceClient(\"{{ accessToken }}\");\n\nconst audio = await client.textToSpeech({\n{% if endpointUrl %}\n    endpointUrl: \"{{ endpointUrl }}\",\n{% endif %}\n    provider: \"{{ provider }}\",\n    model: \"{{ model.id }}\",\n\tinputs: {{ inputs.asObj.inputs }},\n}{% if billTo %}, {\n    billTo: \"{{ billTo }}\",\n}{% endif %});\n// Use the generated audio (it's a Blob)",
            "textToVideo": "import { InferenceClient } from \"@huggingface/inference\";\n\nconst client = new InferenceClient(\"{{ accessToken }}\");\n\nconst video = await client.textToVideo({\n{% if endpointUrl %}\n    endpointUrl: \"{{ endpointUrl }}\",\n{% endif %}\n    provider: \"{{ provider }}\",\n    model: \"{{ model.id }}\",\n\tinputs: {{ inputs.asObj.inputs }},\n}{% if billTo %}, {\n    billTo: \"{{ billTo }}\",\n}{% endif %});\n// Use the generated video (it's a Blob)"
        },
        "openai": {
            "conversational": "import { OpenAI } from \"openai\";\n\nconst client = new OpenAI({\n\tbaseURL: \"{{ baseUrl }}\",\n\tapiKey: \"{{ accessToken }}\",\n{% if billTo %}\n\tdefaultHeaders: {\n\t\t\"X-HF-Bill-To\": \"{{ billTo }}\" \n\t}\n{% endif %}\n});\n\nconst chatCompletion = await client.chat.completions.create({\n\tmodel: \"{{ providerModelId }}\",\n{{ inputs.asTsString }}\n});\n\nconsole.log(chatCompletion.choices[0].message);",
            "conversationalStream": "import { OpenAI } from \"openai\";\n\nconst client = new OpenAI({\n\tbaseURL: \"{{ baseUrl }}\",\n\tapiKey: \"{{ accessToken }}\",\n{% if billTo %}\n    defaultHeaders: {\n\t\t\"X-HF-Bill-To\": \"{{ billTo }}\" \n\t}\n{% endif %}\n});\n\nconst stream = await client.chat.completions.create({\n    model: \"{{ providerModelId }}\",\n{{ inputs.asTsString }}\n    stream: true,\n});\n\nfor await (const chunk of stream) {\n    process.stdout.write(chunk.choices[0]?.delta?.content || \"\");\n}"
        }
    },
    "python": {
        "fal_client": {
            "imageToImage": "{%if provider == \"fal-ai\" %}\nimport fal_client\nimport base64\n\ndef on_queue_update(update):\n    if isinstance(update, fal_client.InProgress):\n        for log in update.logs:\n           print(log[\"message\"])\n\nwith open(\"{{inputs.asObj.inputs}}\", \"rb\") as image_file:\n    image_base_64 = base64.b64encode(image_file.read()).decode('utf-8')\n\nresult = fal_client.subscribe(\n    \"fal-ai/flux-kontext/dev\",\n    arguments={\n        \"prompt\": f\"data:image/png;base64,{image_base_64}\",\n        \"image_url\": \"{{ providerInputs.asObj.inputs }}\",\n    },\n    with_logs=True,\n    on_queue_update=on_queue_update,\n)\nprint(result)\n{%endif%}\n",
            "imageToVideo": "{%if provider == \"fal-ai\" %}\nimport fal_client\nimport base64\n\ndef on_queue_update(update):\n    if isinstance(update, fal_client.InProgress):\n        for log in update.logs:\n           print(log[\"message\"])\n\nwith open(\"{{inputs.asObj.inputs}}\", \"rb\") as image_file:\n    image_base_64 = base64.b64encode(image_file.read()).decode('utf-8')\n\nresult = fal_client.subscribe(\n    \"{{model.id}}\",\n    arguments={\n        \"image_url\": f\"data:image/png;base64,{image_base_64}\",\n        \"prompt\": \"{{inputs.asObj.parameters.prompt}}\",\n    },\n    with_logs=True,\n    on_queue_update=on_queue_update,\n)\nprint(result)\n{%endif%}\n",
            "textToImage": "{% if provider == \"fal-ai\" %}\nimport fal_client\n\n{% if providerInputs.asObj.loras is defined and providerInputs.asObj.loras != none %}\nresult = fal_client.subscribe(\n    \"{{ providerModelId }}\",\n    arguments={\n        \"prompt\": {{ inputs.asObj.inputs }},\n        \"loras\":{{ providerInputs.asObj.loras | tojson }},\n    },\n)\n{% else %}\nresult = fal_client.subscribe(\n    \"{{ providerModelId }}\",\n    arguments={\n        \"prompt\": {{ inputs.asObj.inputs }},\n    },\n)\n{% endif %} \nprint(result)\n{% endif %} "
        },
        "huggingface_hub": {
            "basic": "result = client.{{ methodName }}(\n    {{ inputs.asObj.inputs }},\n    model=\"{{ model.id }}\",\n)",
            "basicAudio": "output = client.{{ methodName }}({{ inputs.asObj.inputs }}, model=\"{{ model.id }}\")",
            "basicImage": "output = client.{{ methodName }}({{ inputs.asObj.inputs }}, model=\"{{ model.id }}\")",
            "conversational": "completion = client.chat.completions.create(\n    model=\"{{ model.id }}\",\n{{ inputs.asPythonString }}\n)\n\nprint(completion.choices[0].message) ",
            "conversationalStream": "stream = client.chat.completions.create(\n    model=\"{{ model.id }}\",\n{{ inputs.asPythonString }}\n    stream=True,\n)\n\nfor chunk in stream:\n    print(chunk.choices[0].delta.content, end=\"\") ",
            "documentQuestionAnswering": "output = client.document_question_answering(\n    \"{{ inputs.asObj.image }}\",\n    question=\"{{ inputs.asObj.question }}\",\n    model=\"{{ model.id }}\",\n) ",
            "imageToImage": "with open(\"{{ inputs.asObj.inputs }}\", \"rb\") as image_file:\n   input_image = image_file.read()\n\n# output is a PIL.Image object\nimage = client.image_to_image(\n    input_image,\n    prompt=\"{{ inputs.asObj.parameters.prompt }}\",\n    model=\"{{ model.id }}\",\n)\n",
            "imageToVideo": "with open(\"{{ inputs.asObj.inputs }}\", \"rb\") as image_file:\n   input_image = image_file.read()\n\nvideo = client.image_to_video(\n    input_image,\n    prompt=\"{{ inputs.asObj.parameters.prompt }}\",\n    model=\"{{ model.id }}\",\n) \n",
            "importInferenceClient": "from huggingface_hub import InferenceClient\n\nclient = InferenceClient(\n{% if endpointUrl %}\n    base_url=\"{{ baseUrl }}\",\n{% endif %}\n    provider=\"{{ provider }}\",\n    api_key=\"{{ accessToken }}\",\n{% if billTo %}\n    bill_to=\"{{ billTo }}\",\n{% endif %}\n)",
            "questionAnswering": "answer = client.question_answering(\n    question=\"{{ inputs.asObj.question }}\",\n    context=\"{{ inputs.asObj.context }}\",\n    model=\"{{ model.id }}\",\n) ",
            "tableQuestionAnswering": "answer = client.table_question_answering(\n    query=\"{{ inputs.asObj.query }}\",\n    table={{ inputs.asObj.table }},\n    model=\"{{ model.id }}\",\n) ",
            "textToImage": "# output is a PIL.Image object\nimage = client.text_to_image(\n    {{ inputs.asObj.inputs }},\n    model=\"{{ model.id }}\",\n) ",
            "textToSpeech": "# audio is returned as bytes\naudio = client.text_to_speech(\n    {{ inputs.asObj.inputs }},\n    model=\"{{ model.id }}\",\n) \n",
            "textToVideo": "video = client.text_to_video(\n    {{ inputs.asObj.inputs }},\n    model=\"{{ model.id }}\",\n) "
        },
        "openai": {
            "conversational": "from openai import OpenAI\n\nclient = OpenAI(\n    base_url=\"{{ baseUrl }}\",\n    api_key=\"{{ accessToken }}\",\n{% if billTo %}\n    default_headers={\n        \"X-HF-Bill-To\": \"{{ billTo }}\"\n    }\n{% endif %}\n)\n\ncompletion = client.chat.completions.create(\n    model=\"{{ providerModelId }}\",\n{{ inputs.asPythonString }}\n)\n\nprint(completion.choices[0].message) ",
            "conversationalStream": "from openai import OpenAI\n\nclient = OpenAI(\n    base_url=\"{{ baseUrl }}\",\n    api_key=\"{{ accessToken }}\",\n{% if billTo %}\n    default_headers={\n        \"X-HF-Bill-To\": \"{{ billTo }}\"\n    }\n{% endif %}\n)\n\nstream = client.chat.completions.create(\n    model=\"{{ providerModelId }}\",\n{{ inputs.asPythonString }}\n    stream=True,\n)\n\nfor chunk in stream:\n    print(chunk.choices[0].delta.content, end=\"\")"
        },
        "requests": {
            "basic": "def query(payload):\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.json()\n\noutput = query({\n    \"inputs\": {{ providerInputs.asObj.inputs }},\n}) ",
            "basicAudio": "def query(filename):\n    with open(filename, \"rb\") as f:\n        data = f.read()\n    response = requests.post(API_URL, headers={\"Content-Type\": \"audio/flac\", **headers}, data=data)\n    return response.json()\n\noutput = query({{ providerInputs.asObj.inputs }})",
            "basicImage": "def query(filename):\n    with open(filename, \"rb\") as f:\n        data = f.read()\n    response = requests.post(API_URL, headers={\"Content-Type\": \"image/jpeg\", **headers}, data=data)\n    return response.json()\n\noutput = query({{ providerInputs.asObj.inputs }})",
            "conversational": "def query(payload):\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.json()\n\nresponse = query({\n{{ autoInputs.asJsonString }}\n})\n\nprint(response[\"choices\"][0][\"message\"])",
            "conversationalStream": "def query(payload):\n    response = requests.post(API_URL, headers=headers, json=payload, stream=True)\n    for line in response.iter_lines():\n        if not line.startswith(b\"data:\"):\n            continue\n        if line.strip() == b\"data: [DONE]\":\n            return\n        yield json.loads(line.decode(\"utf-8\").lstrip(\"data:\").rstrip(\"/n\"))\n\nchunks = query({\n{{ autoInputs.asJsonString }},\n    \"stream\": True,\n})\n\nfor chunk in chunks:\n    print(chunk[\"choices\"][0][\"delta\"][\"content\"], end=\"\")",
            "documentQuestionAnswering": "def query(payload):\n    with open(payload[\"image\"], \"rb\") as f:\n        img = f.read()\n        payload[\"image\"] = base64.b64encode(img).decode(\"utf-8\")\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.json()\n\noutput = query({\n    \"inputs\": {\n        \"image\": \"{{ inputs.asObj.image }}\",\n        \"question\": \"{{ inputs.asObj.question }}\",\n    },\n}) ",
            "imageToImage": "\ndef query(payload):\n    with open(payload[\"inputs\"], \"rb\") as f:\n        img = f.read()\n        payload[\"inputs\"] = base64.b64encode(img).decode(\"utf-8\")\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.content\n\nimage_bytes = query({\n{{ providerInputs.asJsonString }}\n})\n\n# You can access the image with PIL.Image for example\nimport io\nfrom PIL import Image\nimage = Image.open(io.BytesIO(image_bytes)) ",
            "imageToVideo": "\ndef query(payload):\n    with open(payload[\"inputs\"], \"rb\") as f:\n        img = f.read()\n        payload[\"inputs\"] = base64.b64encode(img).decode(\"utf-8\")\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.content\n\nvideo_bytes = query({\n{{ inputs.asJsonString }}\n})\n",
            "importRequests": "{% if importBase64 %}\nimport base64\n{% endif %}\n{% if importJson %}\nimport json\n{% endif %}\nimport requests\n\nAPI_URL = \"{{ fullUrl }}\"\nheaders = {\n    \"Authorization\": \"{{ authorizationHeader }}\",\n{% if billTo %}\n    \"X-HF-Bill-To\": \"{{ billTo }}\"\n{% endif %}\n}",
            "tabular": "def query(payload):\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.content\n\nresponse = query({\n    \"inputs\": {\n        \"data\": {{ providerInputs.asObj.inputs }}\n    },\n}) ",
            "textToAudio": "{% if model.library_name == \"transformers\" %}\ndef query(payload):\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.content\n\naudio_bytes = query({\n    \"inputs\": {{ inputs.asObj.inputs }},\n})\n# You can access the audio with IPython.display for example\nfrom IPython.display import Audio\nAudio(audio_bytes)\n{% else %}\ndef query(payload):\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.json()\n\naudio, sampling_rate = query({\n    \"inputs\": {{ inputs.asObj.inputs }},\n})\n# You can access the audio with IPython.display for example\nfrom IPython.display import Audio\nAudio(audio, rate=sampling_rate)\n{% endif %} ",
            "textToImage": "{% if provider == \"hf-inference\" %}\ndef query(payload):\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.content\n\nimage_bytes = query({\n    \"inputs\": {{ providerInputs.asObj.inputs }},\n})\n\n# You can access the image with PIL.Image for example\nimport io\nfrom PIL import Image\nimage = Image.open(io.BytesIO(image_bytes))\n{% endif %}",
            "textToSpeech": "{% if model.library_name == \"transformers\" %}\ndef query(payload):\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.content\n\naudio_bytes = query({\n    \"text\": {{ inputs.asObj.inputs }},\n})\n# You can access the audio with IPython.display for example\nfrom IPython.display import Audio\nAudio(audio_bytes)\n{% else %}\ndef query(payload):\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.json()\n\naudio, sampling_rate = query({\n    \"text\": {{ inputs.asObj.inputs }},\n})\n# You can access the audio with IPython.display for example\nfrom IPython.display import Audio\nAudio(audio, rate=sampling_rate)\n{% endif %} ",
            "zeroShotClassification": "def query(payload):\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.json()\n\noutput = query({\n    \"inputs\": {{ providerInputs.asObj.inputs }},\n    \"parameters\": {\"candidate_labels\": [\"refund\", \"legal\", \"faq\"]},\n}) ",
            "zeroShotImageClassification": "def query(data):\n    with open(data[\"image_path\"], \"rb\") as f:\n        img = f.read()\n    payload={\n        \"parameters\": data[\"parameters\"],\n        \"inputs\": base64.b64encode(img).decode(\"utf-8\")\n    }\n    response = requests.post(API_URL, headers=headers, json=payload)\n    return response.json()\n\noutput = query({\n    \"image_path\": {{ providerInputs.asObj.inputs }},\n    \"parameters\": {\"candidate_labels\": [\"cat\", \"dog\", \"llama\"]},\n}) "
        }
    },
    "sh": {
        "curl": {
            "basic": "curl {{ fullUrl }} \\\n    -X POST \\\n    -H 'Authorization: {{ authorizationHeader }}' \\\n    -H 'Content-Type: application/json' \\\n{% if billTo %}\n    -H 'X-HF-Bill-To: {{ billTo }}' \\\n{% endif %}\n    -d '{\n{{ providerInputs.asCurlString }}\n    }'",
            "basicAudio": "curl {{ fullUrl }} \\\n    -X POST \\\n    -H 'Authorization: {{ authorizationHeader }}' \\\n    -H 'Content-Type: audio/flac' \\\n{% if billTo %}\n    -H 'X-HF-Bill-To: {{ billTo }}' \\\n{% endif %}\n    --data-binary @{{ providerInputs.asObj.inputs }}",
            "basicImage": "curl {{ fullUrl }} \\\n    -X POST \\\n    -H 'Authorization: {{ authorizationHeader }}' \\\n    -H 'Content-Type: image/jpeg' \\\n{% if billTo %}\n    -H 'X-HF-Bill-To: {{ billTo }}' \\\n{% endif %}\n    --data-binary @{{ providerInputs.asObj.inputs }}",
            "conversational": "curl {{ fullUrl }} \\\n    -H 'Authorization: {{ authorizationHeader }}' \\\n    -H 'Content-Type: application/json' \\\n{% if billTo %}\n    -H 'X-HF-Bill-To: {{ billTo }}' \\\n{% endif %}\n    -d '{\n{{ autoInputs.asCurlString }},\n        \"stream\": false\n    }'",
            "conversationalStream": "curl {{ fullUrl }} \\\n    -H 'Authorization: {{ authorizationHeader }}' \\\n    -H 'Content-Type: application/json' \\\n{% if billTo %}\n    -H 'X-HF-Bill-To: {{ billTo }}' \\\n{% endif %}\n    -d '{\n{{ autoInputs.asCurlString }},\n        \"stream\": true\n    }'",
            "zeroShotClassification": "curl {{ fullUrl }} \\\n    -X POST \\\n    -d '{\"inputs\": {{ providerInputs.asObj.inputs }}, \"parameters\": {\"candidate_labels\": [\"refund\", \"legal\", \"faq\"]}}' \\\n    -H 'Content-Type: application/json' \\\n    -H 'Authorization: {{ authorizationHeader }}'\n{% if billTo %} \\\n    -H 'X-HF-Bill-To: {{ billTo }}'\n{% endif %}"
        }
    }
};
