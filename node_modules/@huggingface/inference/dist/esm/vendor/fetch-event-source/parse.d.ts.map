{"version": 3, "file": "parse.d.ts", "sourceRoot": "", "sources": ["../../../../src/vendor/fetch-event-source/parse.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AAEH;;;GAGG;AACH,MAAM,WAAW,kBAAkB;IAC/B,wEAAwE;IACxE,EAAE,EAAE,MAAM,CAAC;IACX,wDAAwD;IACxD,KAAK,EAAE,MAAM,CAAC;IACd,qBAAqB;IACrB,IAAI,EAAE,MAAM,CAAC;IACb,yFAAyF;IACzF,KAAK,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;;;;GAKG;AACH,wBAAsB,QAAQ,CAAC,MAAM,EAAE,cAAc,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,UAAU,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAMpH;AASD;;;;;GAKG;AACH,wBAAgB,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,EAAE,UAAU,KAAK,IAAI,CAmE3G;AAED;;;;;;GAMG;AACH,wBAAgB,WAAW,CACvB,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,KAAK,IAAI,EAC1B,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,EAChC,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,kBAAkB,KAAK,IAAI,GAC9C,CAAC,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,KAAK,IAAI,CAyCjD"}