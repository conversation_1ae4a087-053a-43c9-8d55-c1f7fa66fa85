import type { DocumentQuestionAnsweringInput, DocumentQuestionAnsweringInputData, DocumentQuestionAnsweringOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type DocumentQuestionAnsweringArgs = BaseArgs & DocumentQuestionAnsweringInput & {
    inputs: DocumentQuestionAnsweringInputData & {
        image: Blob;
    };
};
/**
 * Answers a question on a document image. Recommended model: impira/layoutlm-document-qa.
 */
export declare function documentQuestionAnswering(args: DocumentQuestionAnsweringArgs, options?: Options): Promise<DocumentQuestionAnsweringOutput[number]>;
//# sourceMappingURL=documentQuestionAnswering.d.ts.map