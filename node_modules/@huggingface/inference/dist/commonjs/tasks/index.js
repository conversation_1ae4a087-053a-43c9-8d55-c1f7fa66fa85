"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
// Custom tasks with arbitrary inputs and outputs
__exportStar(require("./custom/request.js"), exports);
__exportStar(require("./custom/streamingRequest.js"), exports);
// Audio tasks
__exportStar(require("./audio/audioClassification.js"), exports);
__exportStar(require("./audio/audioToAudio.js"), exports);
__exportStar(require("./audio/automaticSpeechRecognition.js"), exports);
__exportStar(require("./audio/textToSpeech.js"), exports);
// Computer Vision tasks
__exportStar(require("./cv/imageClassification.js"), exports);
__exportStar(require("./cv/imageSegmentation.js"), exports);
__exportStar(require("./cv/imageToImage.js"), exports);
__exportStar(require("./cv/imageToText.js"), exports);
__exportStar(require("./cv/imageToVideo.js"), exports);
__exportStar(require("./cv/objectDetection.js"), exports);
__exportStar(require("./cv/textToImage.js"), exports);
__exportStar(require("./cv/textToVideo.js"), exports);
__exportStar(require("./cv/zeroShotImageClassification.js"), exports);
// Natural Language Processing tasks
__exportStar(require("./nlp/chatCompletion.js"), exports);
__exportStar(require("./nlp/chatCompletionStream.js"), exports);
__exportStar(require("./nlp/featureExtraction.js"), exports);
__exportStar(require("./nlp/fillMask.js"), exports);
__exportStar(require("./nlp/questionAnswering.js"), exports);
__exportStar(require("./nlp/sentenceSimilarity.js"), exports);
__exportStar(require("./nlp/summarization.js"), exports);
__exportStar(require("./nlp/tableQuestionAnswering.js"), exports);
__exportStar(require("./nlp/textClassification.js"), exports);
__exportStar(require("./nlp/textGeneration.js"), exports);
__exportStar(require("./nlp/textGenerationStream.js"), exports);
__exportStar(require("./nlp/tokenClassification.js"), exports);
__exportStar(require("./nlp/translation.js"), exports);
__exportStar(require("./nlp/zeroShotClassification.js"), exports);
// Multimodal tasks
__exportStar(require("./multimodal/documentQuestionAnswering.js"), exports);
__exportStar(require("./multimodal/visualQuestionAnswering.js"), exports);
// Tabular tasks
__exportStar(require("./tabular/tabularClassification.js"), exports);
__exportStar(require("./tabular/tabularRegression.js"), exports);
