import type { TextGenerationInput, TextGenerationOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type { TextGenerationInput, TextGenerationOutput };
/**
 * Use to continue text from a prompt. This is a very generic task. Recommended model: gpt2 (it’s a simple model, but fun to play with).
 */
export declare function textGeneration(args: BaseArgs & TextGenerationInput, options?: Options): Promise<TextGenerationOutput>;
//# sourceMappingURL=textGeneration.d.ts.map