import type { TableQuestionAnsweringInput, TableQuestionAnsweringOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type TableQuestionAnsweringArgs = BaseArgs & TableQuestionAnsweringInput;
/**
 * Don’t know SQL? Don’t want to dive into a large spreadsheet? Ask questions in plain english! Recommended model: google/tapas-base-finetuned-wtq.
 */
export declare function tableQuestionAnswering(args: TableQuestionAnsweringArgs, options?: Options): Promise<TableQuestionAnsweringOutput[number]>;
//# sourceMappingURL=tableQuestionAnswering.d.ts.map