import type { ObjectDetectionInput, ObjectDetectionOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
import { type LegacyImageInput } from "./utils.js";
export type ObjectDetectionArgs = BaseArgs & (ObjectDetectionInput | LegacyImageInput);
/**
 * This task reads some image input and outputs the likelihood of classes & bounding boxes of detected objects.
 * Recommended model: facebook/detr-resnet-50
 */
export declare function objectDetection(args: ObjectDetectionArgs, options?: Options): Promise<ObjectDetectionOutput>;
//# sourceMappingURL=objectDetection.d.ts.map