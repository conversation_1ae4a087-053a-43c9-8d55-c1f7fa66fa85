import type { ImageToImageInput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type ImageToImageArgs = BaseArgs & ImageToImageInput;
/**
 * This task reads some text input and outputs an image.
 * Recommended model: lllyasviel/sd-controlnet-depth
 */
export declare function imageToImage(args: ImageToImageArgs, options?: Options): Promise<Blob>;
//# sourceMappingURL=imageToImage.d.ts.map