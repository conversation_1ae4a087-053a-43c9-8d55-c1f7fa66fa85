{"version": 3, "file": "hf-inference.d.ts", "sourceRoot": "", "sources": ["../../../src/providers/hf-inference.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AACH,OAAO,KAAK,EACX,yBAAyB,EACzB,gCAAgC,EAChC,oBAAoB,EACpB,+BAA+B,EAC/B,uBAAuB,EACvB,cAAc,EACd,yBAAyB,EACzB,uBAAuB,EACvB,iBAAiB,EACjB,qBAAqB,EACrB,uBAAuB,EACvB,8BAA8B,EAC9B,wBAAwB,EACxB,mBAAmB,EACnB,4BAA4B,EAC5B,wBAAwB,EACxB,oBAAoB,EACpB,yBAAyB,EACzB,iBAAiB,EACjB,6BAA6B,EAC7B,4BAA4B,EAE5B,iCAAiC,EACjC,MAAM,oBAAoB,CAAC;AAG5B,OAAO,KAAK,EAAE,2BAA2B,EAAE,MAAM,2CAA2C,CAAC;AAC7F,OAAO,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAEtE,OAAO,KAAK,EACX,6BAA6B,EAC7B,sBAAsB,EACtB,oCAAoC,EACpC,wBAAwB,EACxB,mCAAmC,EACnC,2BAA2B,EAC3B,kBAAkB,EAClB,6BAA6B,EAC7B,2BAA2B,EAC3B,sBAAsB,EACtB,qBAAqB,EACrB,yBAAyB,EACzB,2BAA2B,EAC3B,4BAA4B,EAC5B,uBAAuB,EACvB,gCAAgC,EAChC,+BAA+B,EAC/B,2BAA2B,EAC3B,4BAA4B,EAC5B,wBAAwB,EACxB,qBAAqB,EACrB,qBAAqB,EACrB,sBAAsB,EACtB,6BAA6B,EAC7B,qBAAqB,EACrB,iCAAiC,EACjC,gCAAgC,EAChC,qCAAqC,EACrC,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AAEzD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AACpE,OAAO,KAAK,EAAE,8BAA8B,EAAE,MAAM,8CAA8C,CAAC;AAEnG,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;AAC9E,UAAU,qBAAqB;IAC9B,IAAI,EAAE,KAAK,CAAC;QACX,QAAQ,EAAE,MAAM,CAAC;KACjB,CAAC,CAAC;CACH;AAED,UAAU,wBAAwB;IACjC,MAAM,EAAE,MAAM,EAAE,CAAC;CACjB;AAED,UAAU,kBAAkB;IAC3B,IAAI,EAAE,MAAM,CAAC;IACb,cAAc,EAAE,MAAM,CAAC;IACvB,KAAK,EAAE,MAAM,CAAC;CACd;AAED,eAAO,MAAM,sCAAsC,wDAAyD,CAAC;AAE7G,qBAAa,eAAgB,SAAQ,kBAAkB;;IAItD,cAAc,CAAC,MAAM,EAAE,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IAGlD,OAAO,CAAC,MAAM,EAAE,SAAS,GAAG,MAAM;IAO3C,SAAS,CAAC,MAAM,EAAE,SAAS,GAAG,MAAM;IAQrB,WAAW,CAAC,QAAQ,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;CAG/D;AAED,qBAAa,0BAA2B,SAAQ,eAAgB,YAAW,qBAAqB;IAChF,WAAW,CACzB,QAAQ,EAAE,qBAAqB,GAAG,wBAAwB,EAC1D,GAAG,CAAC,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,WAAW,EACrB,UAAU,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,GAClC,OAAO,CAAC,MAAM,GAAG,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CAsCnD;AAED,qBAAa,6BAA8B,SAAQ,eAAgB,YAAW,wBAAwB;IAC5F,OAAO,CAAC,MAAM,EAAE,SAAS,GAAG,MAAM;IAkBlC,cAAc,CAAC,MAAM,EAAE,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IAOrD,WAAW,CAAC,QAAQ,EAAE,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;CAGzF;AAED,qBAAa,6BAA8B,SAAQ,eAAgB,YAAW,wBAAwB;IACtF,WAAW,CAAC,QAAQ,EAAE,oBAAoB,GAAG,oBAAoB,EAAE,GAAG,OAAO,CAAC,oBAAoB,CAAC;CASlH;AAED,qBAAa,kCAAmC,SAAQ,eAAgB,YAAW,6BAA6B;IAChG,WAAW,CAAC,QAAQ,EAAE,OAAO,GAAG,OAAO,CAAC,yBAAyB,CAAC;CAcjF;AAED,qBAAa,yCACZ,SAAQ,eACR,YAAW,oCAAoC;IAEhC,WAAW,CAAC,QAAQ,EAAE,gCAAgC,GAAG,OAAO,CAAC,gCAAgC,CAAC;IAI3G,mBAAmB,CAAC,IAAI,EAAE,8BAA8B,GAAG,OAAO,CAAC,WAAW,CAAC;CAQrF;AAED,qBAAa,2BAA4B,SAAQ,eAAgB,YAAW,sBAAsB;IAClF,WAAW,CAAC,QAAQ,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;CA0BzF;AAED,qBAAa,wCACZ,SAAQ,eACR,YAAW,mCAAmC;IAE/B,WAAW,CACzB,QAAQ,EAAE,+BAA+B,GACvC,OAAO,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;CAmBnD;AAED,qBAAa,gCAAiC,SAAQ,eAAgB,YAAW,2BAA2B;IAC5F,WAAW,CAAC,QAAQ,EAAE,uBAAuB,GAAG,OAAO,CAAC,uBAAuB,CAAC;CAgB/F;AAED,qBAAa,kCAAmC,SAAQ,eAAgB,YAAW,6BAA6B;IAChG,WAAW,CAAC,QAAQ,EAAE,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,CAAC;CAQnG;AAED,qBAAa,gCAAiC,SAAQ,eAAgB,YAAW,2BAA2B;IAC5F,WAAW,CAAC,QAAQ,EAAE,uBAAuB,GAAG,OAAO,CAAC,uBAAuB,CAAC;IAiBzF,mBAAmB,CAAC,IAAI,EAAE,qBAAqB,GAAG,OAAO,CAAC,WAAW,CAAC;CAQ5E;AAED,qBAAa,0BAA2B,SAAQ,eAAgB,YAAW,qBAAqB;IAChF,WAAW,CAAC,QAAQ,EAAE,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;CAQnF;AAED,qBAAa,2BAA4B,SAAQ,eAAgB,YAAW,sBAAsB;IAC3F,mBAAmB,CAAC,IAAI,EAAE,gBAAgB,GAAG,OAAO,CAAC,WAAW,CAAC;IAiBxD,WAAW,CAAC,QAAQ,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;CAQzD;AAED,qBAAa,8BAA+B,SAAQ,eAAgB,YAAW,yBAAyB;IACxF,WAAW,CAAC,QAAQ,EAAE,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,CAAC;CAmB3F;AAED,qBAAa,0CACZ,SAAQ,eACR,YAAW,qCAAqC;IAEjC,WAAW,CAAC,QAAQ,EAAE,iCAAiC,GAAG,OAAO,CAAC,iCAAiC,CAAC;CAQnH;AAED,qBAAa,iCAAkC,SAAQ,eAAgB,YAAW,4BAA4B;IAC9F,WAAW,CAAC,QAAQ,EAAE,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAC;CASjG;AAED,qBAAa,gCAAiC,SAAQ,eAAgB,YAAW,2BAA2B;IAC5F,WAAW,CACzB,QAAQ,EAAE,uBAAuB,GAAG,uBAAuB,CAAC,MAAM,CAAC,GACjE,OAAO,CAAC,8BAA8B,CAAC;CAyB1C;AAED,qBAAa,uBAAwB,SAAQ,eAAgB,YAAW,kBAAkB;IAC1E,WAAW,CAAC,QAAQ,EAAE,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;CAiB7E;AAED,qBAAa,qCAAsC,SAAQ,eAAgB,YAAW,gCAAgC;IACtG,WAAW,CAAC,QAAQ,EAAE,OAAO,GAAG,OAAO,CAAC,4BAA4B,CAAC;IA4BpF,OAAO,CAAC,MAAM,CAAC,qBAAqB;CAUpC;AAED,qBAAa,iCAAkC,SAAQ,eAAgB,YAAW,4BAA4B;IAC9F,WAAW,CAAC,QAAQ,EAAE,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAC;CAQjG;AAED,qBAAa,qCAAsC,SAAQ,eAAgB,YAAW,gCAAgC;IACrH,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,IAAI,4BAA4B,CAAC,MAAM,CAAC;IAkB7D,WAAW,CAAC,QAAQ,EAAE,4BAA4B,GAAG,OAAO,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;CAYjH;AAED,qBAAa,kCAAmC,SAAQ,eAAgB,YAAW,6BAA6B;IAChG,WAAW,CAAC,QAAQ,EAAE,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,CAAC;CAkBnG;AAED,qBAAa,0BAA2B,SAAQ,eAAgB,YAAW,qBAAqB;IAChF,WAAW,CAAC,QAAQ,EAAE,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;CAQnF;AAED,qBAAa,4BAA6B,SAAQ,eAAgB,YAAW,uBAAuB;IACpF,WAAW,CAAC,QAAQ,EAAE,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;CAQvF;AAED,qBAAa,2BAA4B,SAAQ,eAAgB,YAAW,sBAAsB;IAClF,WAAW,CAAC,QAAQ,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;CAGzD;AAED,qBAAa,oCAAqC,SAAQ,eAAgB,YAAW,+BAA+B;IACpG,WAAW,CAAC,QAAQ,EAAE,2BAA2B,GAAG,OAAO,CAAC,2BAA2B,CAAC;CAQvG;AAED,qBAAa,sCACZ,SAAQ,eACR,YAAW,iCAAiC;IAE7B,WAAW,CAAC,QAAQ,EAAE,6BAA6B,GAAG,OAAO,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;CAcnH;AAED,qBAAa,gCAAiC,SAAQ,eAAgB,YAAW,2BAA2B;IAC5F,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;CAQjE;AAED,qBAAa,0BAA2B,SAAQ,eAAgB,YAAW,qBAAqB;IAChF,WAAW,CAAC,QAAQ,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;CAGzD"}